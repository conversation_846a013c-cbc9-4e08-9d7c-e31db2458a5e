import { formatIsoToPlainDate, formatIsoToPlainTime } from '@/common/formatDate';
import { SchemaStatus } from '@/const/SchemaStatus';
import { IssuerSchema } from '@/types/issuerSchema';
import clsx from 'clsx';
import { SmallLabelPrimary, TextWithSmallLabel } from './Labels';
import { ButtonBorderSmall } from '@/components/Buttons';
import { useState } from 'react';

interface Props {
    onClick: () => void;
    onAddNewVersionClick: () => void;
    onSchemaOfferingClick: () => void;
    countOfCredentials: number;
    onRestoreVersion: () => void;
    schemaType: string;
    data: IssuerSchema;
    newVersionAvailable: boolean;
    active?: boolean;
    children?: React.ReactNode;
}

export const SchemaListItem = ({
    onAddNewVersionClick,
    onSchemaOfferingClick,
    countOfCredentials,
    data,
    schemaType,
    newVersionAvailable,
    children,
    active,
}: Omit<Props, 'onClick' | 'onRestoreVersion'>) => {
    const [open, setOpen] = useState(false);

    const isActive = active || open;

    return (
        <div
            className={clsx(
                isActive ? '!border-main-100' : '',
                'flex flex-col bg-main-600/5 gap-4 border border-transparent justify-center hover:border-main-100 transition-all duration-300 rounded-lg p-4 cursor-pointer'
            )}
        >
            {/* ITEM HEADER */}
            <div className="flex flex-row justify-between h-full select-none" onClick={() => setOpen(prev => !prev)}>
                <div className="flex flex-col gap-1">
                    <span className="text-lg">{data.name}</span>
                    <span className="text-sm">{schemaType}</span>
                </div>
                <div className="flex flex-col items-end gap-1 text-xs h-full">
                    <SmallLabelPrimary label={`Version: ${data.version}`} />
                    <span>Publish date: {formatIsoToPlainDate(data.createdAt)}</span>
                    <span>Number of Credentials issued: {countOfCredentials}</span>
                </div>
            </div>

            {/* NESTED SCHEMA VERSIONS */}
            {isActive && children ? (
                <>
                    {newVersionAvailable ? (
                        <div className="flex flex-row justify-between items-center">
                            <div
                                id="components_SchemaListItem_button_wspt23"
                                className="text-sm text-main-100 underline"
                                onClick={onSchemaOfferingClick}
                            >
                                Generate Offering
                            </div>
                            <div>
                                <ButtonBorderSmall id="components_SchemaListItem_button_yi3pno"
                                    onClick={onAddNewVersionClick}
                                >
                                    Add New Version
                                </ButtonBorderSmall>
                            </div>
                        </div>
                    ) : null}
                    {children}
                </>
            ) : null}
        </div>
    );
};

export const SchemaVersionListItem = ({ onClick, active, data }: Pick<Props, 'onClick' | 'active' | 'data'>) => {
    const renderDateLabel = () => {
        if (data.status !== SchemaStatus.DRAFT)
            return (
                <div className="flex flex-col text-right items-end justify-center text-xs gap-1">
                    <span>Publish date: {formatIsoToPlainDate(data.createdAt)}</span>
                    <span className="text-xs">Number of Credentials issued: {data.issuedCount}</span>
                </div>
            );
        return (
            <div className="flex flex-col text-right items-end justify-center text-xs gap-1">
                <span>Creation date: {formatIsoToPlainDate(data.createdAt)}</span>
                <span>Last update: {formatIsoToPlainDate(data.updatedAt)}</span>
            </div>
        );
    };

    return (
        <div
            onClick={onClick}
            className={clsx(
                active ? '!border-main-100' : '',
                'flex flex-col border-main-800 justify-center gap-2 hover:border-main-100 transition-all duration-300 rounded-lg border p-4 cursor-pointer'
            )}
        >
            {/* ITEM HEADER */}
            <div className="flex flex-row justify-between select-none">
                <div className="flex flex-col gap-1 justify-center">
                    <div className="flex gap-1 items-center">
                        <span className="text-md">Version: {data.version}</span>
                        {data.status === SchemaStatus.DRAFT ? <SmallLabelPrimary label="(draft)" /> : ''}
                    </div>
                </div>
                {renderDateLabel()}
            </div>
        </div>
    );
};

export const SchemaVersionHistoryListItem = ({
    onClick,
    onRestoreVersion,
    active,
    data,
}: Pick<Props, 'onClick' | 'active' | 'data' | 'onRestoreVersion'>) => {
    return (
        <div
            onClick={onClick}
            className={clsx(
                active ? '!border-main-100' : '',
                'flex flex-col border-main-1100 bg-main-600/5 justify-center gap-2 hover:border-main-100 transition-all duration-300 rounded-lg border p-4 cursor-pointer'
            )}
        >
            {/* ITEM HEADER */}
            <div className="flex flex-row justify-between select-none">
                <div className="flex flex-col gap-1">
                    <TextWithSmallLabel label="Draft to:" text={`v. ${data.version}`} />
                    <span className="text-xs">Creation date: {formatIsoToPlainDate(data.updatedAt)}</span>
                    <span className="text-xs">Creation time: {formatIsoToPlainTime(data.updatedAt)}</span>
                </div>
                <div className="flex flex-col text-right items-end justify-center text-xs gap-1">
                    <ButtonBorderSmall id="components_SchemaListItem_button_xh2jqs" onClick={onRestoreVersion}>
                        Back to this version
                    </ButtonBorderSmall>
                </div>
            </div>
        </div>
    );
};
